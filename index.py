import pandas as pd
import numpy as np

def calculate_airdrop():
    # 读取CSV文件
    df = pd.read_csv('export.csv')
    
    print(f"原始数据行数: {len(df)}")
    print(f"前两行数据预览:")
    print(df.head())
    
    # 排除前两行
    df = df.iloc[2:].reset_index(drop=True)
    print(f"排除前两行后数据行数: {len(df)}")
    
    # 排除持币数量小于3000的地址
    df_filtered = df[df['Balance'] >= 3000].reset_index(drop=True)
    print(f"排除持币数量<3000后数据行数: {len(df_filtered)}")
    
    # 计算总持币数量
    total_balance = df_filtered['Balance'].sum()
    print(f"总持币数量: {total_balance:,.2f}")
    
    # 计算空投代币数量 (1500万 × (当前地址持币数量 / 总持币数量))
    airdrop_amount = 15000000  # 1500万
    df_filtered['AirdropAmount'] = airdrop_amount * (df_filtered['Balance'] / total_balance)
    
    # 显示结果
    print(f"\n计算结果预览:")
    print(df_filtered.head(10))
    
    # 保存结果到新的CSV文件
    output_filename = 'airdrop_result.csv'
    df_filtered.to_csv(output_filename, index=False)
    print(f"\n结果已保存到: {output_filename}")
    
    # 统计信息
    print(f"\n统计信息:")
    print(f"符合条件的地址数量: {len(df_filtered)}")
    print(f"总空投代币数量: {df_filtered['AirdropAmount'].sum():,.2f}")
    print(f"平均每个地址空投数量: {df_filtered['AirdropAmount'].mean():,.2f}")
    print(f"最大空投数量: {df_filtered['AirdropAmount'].max():,.2f}")
    print(f"最小空投数量: {df_filtered['AirdropAmount'].min():,.2f}")

if __name__ == "__main__":
    calculate_airdrop()